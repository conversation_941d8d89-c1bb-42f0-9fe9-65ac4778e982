using System;
using UnityEngine;
using UnityEngine.Rendering;

public class JYCharacterShadow : CharacterShadow
{
    public ActorRender[] ActorRenders => m_ActorRenders;

    private ActorRender[] m_ActorRenders;
    private Transform[] m_ActorRenderParents;
    
    private bool m_Validated = true;
    
    public void Validate()
    {
        m_Validated = true;
    }
    
    public override void OnDrawShadow(CommandBuffer cmd, Action<CommandBuffer, Renderer> onExecute)
    {
        if (m_ActorRenders == null)
            return;
        
        for (int i = 0; i < m_ActorRenders.Length; i++)
        {
            ActorRender render = m_ActorRenders[i];
            for (int j = 0; j < render.RenderList.Count; j++)
            {
                ActorRender.RenderItem renderItem = render.RenderList[j];
                if (!renderItem.notCastShadow)
                {
                    onExecute(cmd, renderItem.renderer);
                }
            }
        }
    }
    
    private void LateUpdate()
    {
        //这一段检测ActorRender的父节点是否发生变化，在上下坐骑或其他情况可能会发生
        //也可以放到业务逻辑中处理
        for (int i = 0; i < m_ActorRenderParents.Length; i++)
        {
            var actorRender = m_ActorRenders[i];
            if (actorRender == null)
                continue;

            Transform parent = actorRender.transform.parent;
            if(parent != m_ActorRenderParents[i])
            {
                m_ActorRenderParents[i] = parent;
                m_Validated = true;
            }
        }
        //
        
        if (m_Validated)
        {
            //Debug.Log("Shadow Validated :" + this.name, this);
            CollectRenders();
            m_Validated = false;
        }
    }
    
    protected override void OnEnable()
    {
        base.OnEnable();
        m_Validated = false;
    }
    
    protected override void CollectRenders()
    {
        m_ActorRenders = this.GetComponentsInChildren<ActorRender>();
        if (m_ActorRenderParents == null || m_ActorRenderParents.Length != m_ActorRenders.Length)
            m_ActorRenderParents = new Transform[m_ActorRenders.Length];

        UpdateLocalBounds();
    }

    protected override Bounds CalculateLocalBounds()
    {
        Bounds bounds = default;
        if (m_ActorRenders == null)
        {
            return default;
        }

        if (m_ActorRenders.Length > 0)
        {
            for (int i = 0; i < m_ActorRenders.Length; i++)
            {
                foreach (var item in m_ActorRenders[i].RenderList)
                {
                    if (bounds.size == Vector3.zero)
                    {
                        bounds = item.renderer.bounds;
                        continue;
                    }

                    bounds.Encapsulate(item.renderer.bounds);
                }
            }
            bounds.center -= this.transform.position;
        }

        float len = Mathf.Max(bounds.size.x + bounds.center.x, bounds.size.z + bounds.center.z) + m_Bias.x;
        if (len <= 0)
        {
            return default;
        }
        bounds.size = new Vector3(len, bounds.size.y * m_Bias.y, len);

        return bounds;
    }
    
    public static JYCharacterShadow Create(GameObject obj, int shadowLevel = 0)
    {
        return CharacterShadow.Create<JYCharacterShadow>(obj, shadowLevel);
    }
}