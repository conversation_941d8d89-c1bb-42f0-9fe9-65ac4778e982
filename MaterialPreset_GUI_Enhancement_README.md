# TextMeshPro MaterialPreset GUI 增强功能

## 功能概述

这个增强功能改进了TextMeshPro的MaterialPreset下拉列表，将原本的单一下拉列表改为分类显示的界面，大大提高了在有大量材质球（如108个）时的使用效率。

## 主要特性

### 1. 智能分类
- 根据材质命名规律自动分类
- 支持的分类：默认、O1、O2、O3、S1、S2、S3等
- 材质命名规律：`TMP_Font_SDF_O1_000000`
  - `TMP_Font_SDF_` - 固定前缀
  - `O1` - Outline 1像素 / `S2` - Shadow 2像素
  - `000000` - 颜色的十六进制值

### 2. 改进的GUI布局
- **顶部分类按钮**：以网格形式显示所有分类
- **颜色列表**：每行显示：
  - 选择按钮（单选）
  - 颜色十六进制字符串
  - 颜色预览块
  - 材质名称（简化显示）

### 3. 用户体验优化
- 自动选择当前材质所在分类
- 滚动视图支持大量材质
- 清晰的视觉分隔
- 材质数量统计显示

## 技术实现

### 修改的文件
- `Scripts/Editor/TMP_BaseEditorPanel.cs` - 主要实现文件
- `Scripts/Editor/TMP_MaterialPresetTest.cs` - 测试工具

### 核心方法
1. `ParseMaterialName()` - 解析材质名称获取分类信息
2. `OrganizeMaterialPresetsByCategory()` - 按分类组织材质
3. `DrawCustomMaterialPresetGUI()` - 绘制新的GUI界面

### 数据结构
```csharp
protected struct MaterialCategoryInfo
{
    public string category;      // 分类名称（如"O1", "S2"）
    public string colorHex;      // 颜色十六进制
    public string displayName;   // 显示名称
    public bool isDefault;       // 是否为默认材质
}
```

## 使用方法

1. 在场景中选择任何TextMeshPro组件
2. 在Inspector面板中找到"Material Preset"部分
3. 使用顶部的分类按钮切换不同类型的材质
4. 在对应分类下选择所需的颜色材质

## 测试工具

通过菜单 `TextMeshPro > Test Material Preset GUI` 可以打开测试窗口，提供：
- 功能说明
- 快速选择TextMeshPro对象
- 材质命名规律说明

## 兼容性

- 完全兼容现有的TextMeshPro功能
- 不影响原有的材质选择逻辑
- 支持所有TextMeshPro组件类型

## 性能优化

- 材质分类在获取时进行，避免重复计算
- 使用字典缓存提高查找效率
- 滚动视图限制同时渲染的元素数量

## 未来扩展

- 支持自定义分类规则
- 添加材质预览功能
- 支持材质搜索和过滤
- 添加收藏夹功能
