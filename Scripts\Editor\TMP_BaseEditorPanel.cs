using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;


namespace TMPro.EditorUtilities
{
    public abstract class TMP_BaseEditorPanel : Editor
    {
        //Labels and Tooltips
        static readonly GUIContent k_RtlToggleLabel = new GUIContent("Enable RTL Editor", "Reverses text direction and allows right to left editing.");
        //static readonly GUIContent k_MainSettingsLabel = new GUIContent("Main Settings");
        static readonly GUIContent k_FontAssetLabel = new GUIContent("Font Asset", "The Font Asset containing the glyphs that can be rendered for this text.");
        static readonly GUIContent k_MaterialPresetLabel = new GUIContent("Material Preset", "The material used for rendering. Only materials created from the Font Asset can be used.");
        static readonly GUIContent k_StyleLabel = new GUIContent("Text Style", "The style from a style sheet to be applied to the text.");
        static readonly GUIContent k_AutoSizeLabel = new GUIContent("Auto Size", "Auto sizes the text to fit the available space.");
        static readonly GUIContent k_FontSizeLabel = new GUIContent("Font Size", "The size the text will be rendered at in points.");
        static readonly GUIContent k_AutoSizeOptionsLabel = new GUIContent("Auto Size Options");
        static readonly GUIContent k_MinLabel = new GUIContent("Min", "The minimum font size.");
        static readonly GUIContent k_MaxLabel = new GUIContent("Max", "The maximum font size.");
        static readonly GUIContent k_WdLabel = new GUIContent("WD%", "Compresses character width up to this value before reducing font size.");
        static readonly GUIContent k_LineLabel = new GUIContent("Line", "Negative value only. Compresses line height down to this value before reducing font size.");
        static readonly GUIContent k_FontStyleLabel = new GUIContent("Font Style", "Styles to apply to the text such as Bold or Italic.");

        static readonly GUIContent k_BoldLabel = new GUIContent("B", "Bold");
        static readonly GUIContent k_ItalicLabel = new GUIContent("I", "Italic");
        static readonly GUIContent k_UnderlineLabel = new GUIContent("U", "Underline");
        static readonly GUIContent k_StrikethroughLabel = new GUIContent("S", "Strikethrough");
        static readonly GUIContent k_LowercaseLabel = new GUIContent("ab", "Lowercase");
        static readonly GUIContent k_UppercaseLabel = new GUIContent("AB", "Uppercase");
        static readonly GUIContent k_SmallcapsLabel = new GUIContent("SC", "Smallcaps");

        static readonly GUIContent k_ColorModeLabel = new GUIContent("Color Mode", "The type of gradient to use.");
        static readonly GUIContent k_BaseColorLabel = new GUIContent("Vertex Color", "The base color of the text vertices.");
        static readonly GUIContent k_ColorPresetLabel = new GUIContent("Color Preset", "A Color Preset which override the local color settings.");
        static readonly GUIContent k_ColorGradientLabel = new GUIContent("Color Gradient", "The gradient color applied over the Vertex Color. Can be locally set or driven by a Gradient Asset.");
        static readonly GUIContent k_CorenerColorsLabel = new GUIContent("Colors", "The color composition of the gradient.");
        static readonly GUIContent k_OverrideTagsLabel = new GUIContent("Override Tags", "Whether the color settings override the <color> tag.");

        static readonly GUIContent k_SpacingOptionsLabel = new GUIContent("Spacing Options (em)", "Spacing adjustments between different elements of the text. Values are in font units where a value of 1 equals 1/100em.");
        static readonly GUIContent k_CharacterSpacingLabel = new GUIContent("Character");
        static readonly GUIContent k_WordSpacingLabel = new GUIContent("Word");
        static readonly GUIContent k_LineSpacingLabel = new GUIContent("Line");
        static readonly GUIContent k_ParagraphSpacingLabel = new GUIContent("Paragraph");

        static readonly GUIContent k_AlignmentLabel = new GUIContent("Alignment", "Horizontal and vertical aligment of the text within its container.");
        static readonly GUIContent k_WrapMixLabel = new GUIContent("Wrap Mix (W <-> C)", "How much to favor words versus characters when distributing the text.");

        static readonly GUIContent k_WrappingLabel = new GUIContent("Wrapping", "Wraps text to the next line when reaching the edge of the container.");
        static readonly GUIContent[] k_WrappingOptions = { new GUIContent("Disabled"), new GUIContent("Enabled") };
        static readonly GUIContent k_OverflowLabel = new GUIContent("Overflow", "How to display text which goes past the edge of the container.");

        static readonly GUIContent k_MarginsLabel = new GUIContent("Margins", "The space between the text and the edge of its container.");
        static readonly GUIContent k_GeometrySortingLabel = new GUIContent("Geometry Sorting", "The order in which text geometry is sorted. Used to adjust the way overlapping characters are displayed.");
        static readonly GUIContent k_IsTextObjectScaleStatic = new GUIContent("Is Scale Static", "Controls whether a text object will be excluded from the InteralUpdate callback to handle scale changes of the text object or its parent(s).");
        static readonly GUIContent k_RichTextLabel = new GUIContent("Rich Text", "Enables the use of rich text tags such as <color> and <font>.");
        static readonly GUIContent k_EscapeCharactersLabel = new GUIContent("Parse Escape Characters", "Whether to display strings such as \"\\n\" as is or replace them by the character they represent.");
        static readonly GUIContent k_VisibleDescenderLabel = new GUIContent("Visible Descender", "Compute descender values from visible characters only. Used to adjust layout behavior when hiding and revealing characters dynamically.");
        static readonly GUIContent k_SpriteAssetLabel = new GUIContent("Sprite Asset", "The Sprite Asset used when NOT specifically referencing one using <sprite=\"Sprite Asset Name\">.");
        static readonly GUIContent k_StyleSheetAssetLabel = new GUIContent("Style Sheet Asset", "The Style Sheet Asset used by this text object.");

        static readonly GUIContent k_HorizontalMappingLabel = new GUIContent("Horizontal Mapping", "Horizontal UV mapping when using a shader with a texture face option.");
        static readonly GUIContent k_VerticalMappingLabel = new GUIContent("Vertical Mapping", "Vertical UV mapping when using a shader with a texture face option.");
        static readonly GUIContent k_LineOffsetLabel = new GUIContent("Line Offset", "Adds an horizontal offset to each successive line. Used for slanted texturing.");

        static readonly GUIContent k_KerningLabel = new GUIContent("Kerning", "Enables character specific spacing between pairs of characters.");
        static readonly GUIContent k_PaddingLabel = new GUIContent("Extra Padding", "Adds some padding between the characters and the edge of the text mesh. Can reduce graphical errors when displaying small text.");

        static readonly GUIContent k_LeftLabel = new GUIContent("Left");
        static readonly GUIContent k_TopLabel = new GUIContent("Top");
        static readonly GUIContent k_RightLabel = new GUIContent("Right");
        static readonly GUIContent k_BottomLabel = new GUIContent("Bottom");

        protected static readonly GUIContent k_ExtraSettingsLabel = new GUIContent("Extra Settings");
        protected static string[] k_UiStateLabel = new string[] { "<i>(Click to collapse)</i> ", "<i>(Click to expand)</i> " };

        static Dictionary<int, TMP_Style> k_AvailableStyles = new Dictionary<int, TMP_Style>();
        protected Dictionary<int, int> m_TextStyleIndexLookup = new Dictionary<int, int>();

        protected struct Foldout
        {
            // Track Inspector foldout panel states, globally.
            public static bool extraSettings = false;
            public static bool materialInspector = true;
        }

        protected static int s_EventId;

        public int selAlignGridA;
        public int selAlignGridB;

        protected SerializedProperty m_TextProp;

        protected SerializedProperty m_IsRightToLeftProp;
        protected string m_RtlText;

        protected SerializedProperty m_FontAssetProp;

        protected SerializedProperty m_FontSharedMaterialProp;
        protected Material[] m_MaterialPresets;
        protected GUIContent[] m_MaterialPresetNames;
        protected Dictionary<int, int> m_MaterialPresetIndexLookup = new Dictionary<int, int>();
        protected int m_MaterialPresetSelectionIndex;
        protected bool m_IsPresetListDirty;

        // 材质分类相关
        protected Dictionary<string, List<Material>> m_MaterialPresetsByCategory = new Dictionary<string, List<Material>>();
        protected string[] m_MaterialCategories;
        protected int m_SelectedCategoryIndex;
        protected Vector2 m_MaterialScrollPosition;
        protected Material m_LastSelectedMaterial; // 记录上次选择的材质
        protected bool m_IsInitializing = true; // 标记是否正在初始化

        protected List<TMP_Style> m_Styles = new List<TMP_Style>();
        protected GUIContent[] m_StyleNames;
        protected int m_StyleSelectionIndex;

        protected SerializedProperty m_FontStyleProp;

        protected SerializedProperty m_FontColorProp;
        protected SerializedProperty m_EnableVertexGradientProp;
        protected SerializedProperty m_FontColorGradientProp;
        protected SerializedProperty m_FontColorGradientPresetProp;
        protected SerializedProperty m_OverrideHtmlColorProp;

        protected SerializedProperty m_FontSizeProp;
        protected SerializedProperty m_FontSizeBaseProp;

        protected SerializedProperty m_AutoSizingProp;
        protected SerializedProperty m_FontSizeMinProp;
        protected SerializedProperty m_FontSizeMaxProp;

        protected SerializedProperty m_LineSpacingMaxProp;
        protected SerializedProperty m_CharWidthMaxAdjProp;

        protected SerializedProperty m_CharacterSpacingProp;
        protected SerializedProperty m_WordSpacingProp;
        protected SerializedProperty m_LineSpacingProp;
        protected SerializedProperty m_ParagraphSpacingProp;

        protected SerializedProperty m_TextAlignmentProp;

        protected SerializedProperty m_HorizontalAlignmentProp;
        protected SerializedProperty m_VerticalAlignmentProp;

        protected SerializedProperty m_HorizontalMappingProp;
        protected SerializedProperty m_VerticalMappingProp;
        protected SerializedProperty m_UvLineOffsetProp;

        protected SerializedProperty m_EnableWordWrappingProp;
        protected SerializedProperty m_WordWrappingRatiosProp;
        protected SerializedProperty m_TextOverflowModeProp;
        protected SerializedProperty m_PageToDisplayProp;
        protected SerializedProperty m_LinkedTextComponentProp;
        protected SerializedProperty m_ParentLinkedTextComponentProp;

        protected SerializedProperty m_EnableKerningProp;

        protected SerializedProperty m_IsRichTextProp;

        protected SerializedProperty m_HasFontAssetChangedProp;

        protected SerializedProperty m_EnableExtraPaddingProp;
        protected SerializedProperty m_CheckPaddingRequiredProp;
        protected SerializedProperty m_EnableEscapeCharacterParsingProp;
        protected SerializedProperty m_UseMaxVisibleDescenderProp;
        protected SerializedProperty m_GeometrySortingOrderProp;
        protected SerializedProperty m_IsTextObjectScaleStaticProp;

        protected SerializedProperty m_SpriteAssetProp;

        protected SerializedProperty m_StyleSheetAssetProp;
        protected SerializedProperty m_TextStyleHashCodeProp;

        protected SerializedProperty m_MarginProp;

        protected SerializedProperty m_ColorModeProp;

        protected bool m_HavePropertiesChanged;

        protected TMP_Text m_TextComponent;
        protected TMP_Text m_PreviousLinkedTextComponent;
        protected RectTransform m_RectTransform;

        protected Material m_TargetMaterial;

        protected Vector3[] m_RectCorners = new Vector3[4];
        protected Vector3[] m_HandlePoints = new Vector3[4];

        protected virtual void OnEnable()
        {
            m_TextProp = serializedObject.FindProperty("m_text");
            m_IsRightToLeftProp = serializedObject.FindProperty("m_isRightToLeft");
            m_FontAssetProp = serializedObject.FindProperty("m_fontAsset");
            m_FontSharedMaterialProp = serializedObject.FindProperty("m_sharedMaterial");

            m_FontStyleProp = serializedObject.FindProperty("m_fontStyle");

            m_FontSizeProp = serializedObject.FindProperty("m_fontSize");
            m_FontSizeBaseProp = serializedObject.FindProperty("m_fontSizeBase");

            m_AutoSizingProp = serializedObject.FindProperty("m_enableAutoSizing");
            m_FontSizeMinProp = serializedObject.FindProperty("m_fontSizeMin");
            m_FontSizeMaxProp = serializedObject.FindProperty("m_fontSizeMax");

            m_LineSpacingMaxProp = serializedObject.FindProperty("m_lineSpacingMax");
            m_CharWidthMaxAdjProp = serializedObject.FindProperty("m_charWidthMaxAdj");

            // Colors & Gradient
            m_FontColorProp = serializedObject.FindProperty("m_fontColor");
            m_EnableVertexGradientProp = serializedObject.FindProperty("m_enableVertexGradient");
            m_FontColorGradientProp = serializedObject.FindProperty("m_fontColorGradient");
            m_FontColorGradientPresetProp = serializedObject.FindProperty("m_fontColorGradientPreset");
            m_OverrideHtmlColorProp = serializedObject.FindProperty("m_overrideHtmlColors");

            m_CharacterSpacingProp = serializedObject.FindProperty("m_characterSpacing");
            m_WordSpacingProp = serializedObject.FindProperty("m_wordSpacing");
            m_LineSpacingProp = serializedObject.FindProperty("m_lineSpacing");
            m_ParagraphSpacingProp = serializedObject.FindProperty("m_paragraphSpacing");

            m_TextAlignmentProp = serializedObject.FindProperty("m_textAlignment");
            m_HorizontalAlignmentProp = serializedObject.FindProperty("m_HorizontalAlignment");
            m_VerticalAlignmentProp = serializedObject.FindProperty("m_VerticalAlignment");

            m_HorizontalMappingProp = serializedObject.FindProperty("m_horizontalMapping");
            m_VerticalMappingProp = serializedObject.FindProperty("m_verticalMapping");
            m_UvLineOffsetProp = serializedObject.FindProperty("m_uvLineOffset");

            m_EnableWordWrappingProp = serializedObject.FindProperty("m_enableWordWrapping");
            m_WordWrappingRatiosProp = serializedObject.FindProperty("m_wordWrappingRatios");
            m_TextOverflowModeProp = serializedObject.FindProperty("m_overflowMode");
            m_PageToDisplayProp = serializedObject.FindProperty("m_pageToDisplay");
            m_LinkedTextComponentProp = serializedObject.FindProperty("m_linkedTextComponent");
            m_ParentLinkedTextComponentProp = serializedObject.FindProperty("parentLinkedComponent");

            m_EnableKerningProp = serializedObject.FindProperty("m_enableKerning");

            m_EnableExtraPaddingProp = serializedObject.FindProperty("m_enableExtraPadding");
            m_IsRichTextProp = serializedObject.FindProperty("m_isRichText");
            m_CheckPaddingRequiredProp = serializedObject.FindProperty("checkPaddingRequired");
            m_EnableEscapeCharacterParsingProp = serializedObject.FindProperty("m_parseCtrlCharacters");
            m_UseMaxVisibleDescenderProp = serializedObject.FindProperty("m_useMaxVisibleDescender");

            m_GeometrySortingOrderProp = serializedObject.FindProperty("m_geometrySortingOrder");
            m_IsTextObjectScaleStaticProp = serializedObject.FindProperty("m_IsTextObjectScaleStatic");

            m_SpriteAssetProp = serializedObject.FindProperty("m_spriteAsset");

            m_StyleSheetAssetProp = serializedObject.FindProperty("m_StyleSheet");
            m_TextStyleHashCodeProp = serializedObject.FindProperty("m_TextStyleHashCode");

            m_MarginProp = serializedObject.FindProperty("m_margin");

            m_HasFontAssetChangedProp = serializedObject.FindProperty("m_hasFontAssetChanged");

            m_ColorModeProp = serializedObject.FindProperty("m_colorMode");

            m_TextComponent = (TMP_Text)target;
            m_RectTransform = m_TextComponent.rectTransform;

            // Restore Previous Linked Text Component
            m_PreviousLinkedTextComponent = m_TextComponent.linkedTextComponent;

            // Create new Material Editor if one does not exists
            m_TargetMaterial = m_TextComponent.fontSharedMaterial;

            // Set material inspector visibility
            if (m_TargetMaterial != null)
                UnityEditorInternal.InternalEditorUtility.SetIsInspectorExpanded(m_TargetMaterial, Foldout.materialInspector);

            // Find all Material Presets matching the current Font Asset Material
            m_MaterialPresetNames = GetMaterialPresets();

            // 初始化分类选择和标志
            m_SelectedCategoryIndex = 0;
            m_LastSelectedMaterial = null;
            m_IsInitializing = true;

            // Get Styles from Style Sheet
            if (TMP_Settings.instance != null)
                m_StyleNames = GetStyleNames();

            // Register to receive events when style sheets are modified.
            TMPro_EventManager.TEXT_STYLE_PROPERTY_EVENT.Add(ON_TEXT_STYLE_CHANGED);

            // Initialize the Event Listener for Undo Events.
            Undo.undoRedoPerformed += OnUndoRedo;
        }

        protected virtual void OnDisable()
        {
            // Set material inspector visibility
            if (m_TargetMaterial != null)
                Foldout.materialInspector = UnityEditorInternal.InternalEditorUtility.GetIsInspectorExpanded(m_TargetMaterial);

            if (Undo.undoRedoPerformed != null)
                Undo.undoRedoPerformed -= OnUndoRedo;

            // Unregister from style sheet related events.
            TMPro_EventManager.TEXT_STYLE_PROPERTY_EVENT.Remove(ON_TEXT_STYLE_CHANGED);
        }

        // Event received when Text Styles are changed.
        void ON_TEXT_STYLE_CHANGED(bool isChanged)
        {
            m_StyleNames = GetStyleNames();
        }

        public override void OnInspectorGUI()
        {
            // Make sure Multi selection only includes TMP Text objects.
            if (IsMixSelectionTypes()) return;

            serializedObject.Update();

            DrawTextInput();

            DrawMainSettings();

            DrawExtraSettings();

            EditorGUILayout.Space();

            if (serializedObject.ApplyModifiedProperties() || m_HavePropertiesChanged)
            {
                m_TextComponent.havePropertiesChanged = true;
                m_HavePropertiesChanged = false;
                EditorUtility.SetDirty(target);
            }
        }

        public void OnSceneGUI()
        {
            if (IsMixSelectionTypes()) return;

            // Margin Frame & Handles
            m_RectTransform.GetWorldCorners(m_RectCorners);
            Vector4 marginOffset = m_TextComponent.margin;
            Vector3 lossyScale = m_RectTransform.lossyScale;

            m_HandlePoints[0] = m_RectCorners[0] + m_RectTransform.TransformDirection(new Vector3(marginOffset.x * lossyScale.x, marginOffset.w * lossyScale.y, 0));
            m_HandlePoints[1] = m_RectCorners[1] + m_RectTransform.TransformDirection(new Vector3(marginOffset.x * lossyScale.x, -marginOffset.y * lossyScale.y, 0));
            m_HandlePoints[2] = m_RectCorners[2] + m_RectTransform.TransformDirection(new Vector3(-marginOffset.z * lossyScale.x, -marginOffset.y * lossyScale.y, 0));
            m_HandlePoints[3] = m_RectCorners[3] + m_RectTransform.TransformDirection(new Vector3(-marginOffset.z * lossyScale.x, marginOffset.w * lossyScale.y, 0));

            Handles.DrawSolidRectangleWithOutline(m_HandlePoints, new Color32(255, 255, 255, 0), new Color32(255, 255, 0, 255));

            Matrix4x4 matrix = m_RectTransform.worldToLocalMatrix;

            // Draw & process FreeMoveHandles

            // LEFT HANDLE
            Vector3 oldLeft = (m_HandlePoints[0] + m_HandlePoints[1]) * 0.5f;
            Vector3 newLeft = Handles.FreeMoveHandle(oldLeft, Quaternion.identity, HandleUtility.GetHandleSize(m_RectTransform.position) * 0.05f, Vector3.zero, Handles.DotHandleCap);
            bool hasChanged = false;
            if (oldLeft != newLeft)
            {
                oldLeft = matrix.MultiplyPoint(oldLeft);
                newLeft = matrix.MultiplyPoint(newLeft);

                float delta = (oldLeft.x - newLeft.x) * lossyScale.x;
                marginOffset.x += -delta / lossyScale.x;
                //Debug.Log("Left Margin H0:" + handlePoints[0] + "  H1:" + handlePoints[1]);
                hasChanged = true;
            }

            // TOP HANDLE
            Vector3 oldTop = (m_HandlePoints[1] + m_HandlePoints[2]) * 0.5f;
            Vector3 newTop = Handles.FreeMoveHandle(oldTop, Quaternion.identity, HandleUtility.GetHandleSize(m_RectTransform.position) * 0.05f, Vector3.zero, Handles.DotHandleCap);
            if (oldTop != newTop)
            {
                oldTop = matrix.MultiplyPoint(oldTop);
                newTop = matrix.MultiplyPoint(newTop);

                float delta = (oldTop.y - newTop.y) * lossyScale.y;
                marginOffset.y += delta / lossyScale.y;
                //Debug.Log("Top Margin H1:" + handlePoints[1] + "  H2:" + handlePoints[2]);
                hasChanged = true;
            }

            // RIGHT HANDLE
            Vector3 oldRight = (m_HandlePoints[2] + m_HandlePoints[3]) * 0.5f;
            Vector3 newRight = Handles.FreeMoveHandle(oldRight, Quaternion.identity, HandleUtility.GetHandleSize(m_RectTransform.position) * 0.05f, Vector3.zero, Handles.DotHandleCap);
            if (oldRight != newRight)
            {
                oldRight = matrix.MultiplyPoint(oldRight);
                newRight = matrix.MultiplyPoint(newRight);

                float delta = (oldRight.x - newRight.x) * lossyScale.x;
                marginOffset.z += delta / lossyScale.x;
                hasChanged = true;
                //Debug.Log("Right Margin H2:" + handlePoints[2] + "  H3:" + handlePoints[3]);
            }

            // BOTTOM HANDLE
            Vector3 oldBottom = (m_HandlePoints[3] + m_HandlePoints[0]) * 0.5f;
            Vector3 newBottom = Handles.FreeMoveHandle(oldBottom, Quaternion.identity, HandleUtility.GetHandleSize(m_RectTransform.position) * 0.05f, Vector3.zero, Handles.DotHandleCap);
            if (oldBottom != newBottom)
            {
                oldBottom = matrix.MultiplyPoint(oldBottom);
                newBottom = matrix.MultiplyPoint(newBottom);

                float delta = (oldBottom.y - newBottom.y) * lossyScale.y;
                marginOffset.w += -delta / lossyScale.y;
                hasChanged = true;
                //Debug.Log("Bottom Margin H0:" + handlePoints[0] + "  H3:" + handlePoints[3]);
            }

            if (hasChanged)
            {
                Undo.RecordObjects(new Object[] {m_RectTransform, m_TextComponent }, "Margin Changes");
                m_TextComponent.margin = marginOffset;
                EditorUtility.SetDirty(target);
            }
        }

        protected void DrawTextInput()
        {
            EditorGUILayout.Space();

            Rect rect = EditorGUILayout.GetControlRect(false, 22);
            GUI.Label(rect, new GUIContent("<b>Text Input</b>"), TMP_UIStyleManager.sectionHeader);

            EditorGUI.indentLevel = 0;

            // If the text component is linked, disable the text input box.
            if (m_ParentLinkedTextComponentProp.objectReferenceValue != null)
            {
                EditorGUILayout.HelpBox("The Text Input Box is disabled due to this text component being linked to another.", MessageType.Info);
            }
            else
            {
                // Display RTL Toggle
                float labelWidth = EditorGUIUtility.labelWidth;
                EditorGUIUtility.labelWidth = 110f;

                m_IsRightToLeftProp.boolValue = EditorGUI.Toggle(new Rect(rect.width - 120, rect.y + 3, 130, 20), k_RtlToggleLabel, m_IsRightToLeftProp.boolValue);

                EditorGUIUtility.labelWidth = labelWidth;

                EditorGUI.BeginChangeCheck();
                EditorGUILayout.PropertyField(m_TextProp, GUIContent.none);

                // Need to also compare string content due to issue related to scroll bar drag handle
                if (EditorGUI.EndChangeCheck() && m_TextProp.stringValue != m_TextComponent.text)
                {
                    m_TextComponent.m_inputSource = TMP_Text.TextInputSources.TextInputBox;
                    m_HavePropertiesChanged = true;
                }

                if (m_IsRightToLeftProp.boolValue)
                {
                    // Copy source text to RTL string
                    m_RtlText = string.Empty;
                    string sourceText = m_TextProp.stringValue;

                    // Reverse Text displayed in Text Input Box
                    for (int i = 0; i < sourceText.Length; i++)
                        m_RtlText += sourceText[sourceText.Length - i - 1];

                    GUILayout.Label("RTL Text Input");

                    EditorGUI.BeginChangeCheck();
                    m_RtlText = EditorGUILayout.TextArea(m_RtlText, TMP_UIStyleManager.wrappingTextArea, GUILayout.Height(EditorGUI.GetPropertyHeight(m_TextProp) - EditorGUIUtility.singleLineHeight), GUILayout.ExpandWidth(true));

                    if (EditorGUI.EndChangeCheck())
                    {
                        // Convert RTL input
                        sourceText = string.Empty;

                        // Reverse Text displayed in Text Input Box
                        for (int i = 0; i < m_RtlText.Length; i++)
                            sourceText += m_RtlText[m_RtlText.Length - i - 1];

                        m_TextProp.stringValue = sourceText;
                    }
                }

                // TEXT STYLE
                if (m_StyleNames != null)
                {
                    rect = EditorGUILayout.GetControlRect(false, 17);

                    EditorGUI.BeginProperty(rect, k_StyleLabel, m_TextStyleHashCodeProp);

                    m_TextStyleIndexLookup.TryGetValue(m_TextStyleHashCodeProp.intValue, out m_StyleSelectionIndex);

                    EditorGUI.BeginChangeCheck();
                    m_StyleSelectionIndex = EditorGUI.Popup(rect, k_StyleLabel, m_StyleSelectionIndex, m_StyleNames);
                    if (EditorGUI.EndChangeCheck())
                    {
                        m_TextStyleHashCodeProp.intValue = m_Styles[m_StyleSelectionIndex].hashCode;
                        m_TextComponent.m_TextStyle = m_Styles[m_StyleSelectionIndex];
                        m_HavePropertiesChanged = true;
                    }

                    EditorGUI.EndProperty();
                }
            }
        }

        protected void DrawMainSettings()
        {
            // MAIN SETTINGS SECTION
            GUILayout.Label(new GUIContent("<b>Main Settings</b>"), TMP_UIStyleManager.sectionHeader);

            //EditorGUI.indentLevel += 1;

            DrawFont();

            DrawColor();

            DrawSpacing();

            DrawAlignment();

            DrawWrappingOverflow();

            DrawTextureMapping();

            //EditorGUI.indentLevel -= 1;
        }

        void DrawFont()
        {
            bool isFontAssetDirty = false;

            // FONT ASSET
            EditorGUI.BeginChangeCheck();
            EditorGUILayout.PropertyField(m_FontAssetProp, k_FontAssetLabel);
            if (EditorGUI.EndChangeCheck())
            {
                m_HavePropertiesChanged = true;
                m_HasFontAssetChangedProp.boolValue = true;

                // Get new Material Presets for the new font asset
                m_MaterialPresetNames = GetMaterialPresets();
                m_MaterialPresetSelectionIndex = 0;

                isFontAssetDirty = true;
            }

            Rect rect;

            // MATERIAL PRESET - 使用自定义GUI
            if (m_MaterialPresetNames != null && !isFontAssetDirty )
            {
                EditorGUI.BeginProperty(new Rect(), k_MaterialPresetLabel, m_FontSharedMaterialProp);
                DrawCustomMaterialPresetGUI();
                EditorGUI.EndProperty();
            }

            // FONT STYLE
            EditorGUI.BeginChangeCheck();

            int v1, v2, v3, v4, v5, v6, v7;

            if (EditorGUIUtility.wideMode)
            {
                rect = EditorGUILayout.GetControlRect(true, EditorGUIUtility.singleLineHeight + 2f);

                EditorGUI.BeginProperty(rect, k_FontStyleLabel, m_FontStyleProp);

                EditorGUI.PrefixLabel(rect, k_FontStyleLabel);

                int styleValue = m_FontStyleProp.intValue;

                rect.x += EditorGUIUtility.labelWidth;
                rect.width -= EditorGUIUtility.labelWidth;

                rect.width = Mathf.Max(25f, rect.width / 7f);

                v1 = TMP_EditorUtility.EditorToggle(rect, (styleValue & 1) == 1, k_BoldLabel, TMP_UIStyleManager.alignmentButtonLeft) ? 1 : 0; // Bold
                rect.x += rect.width;
                v2 = TMP_EditorUtility.EditorToggle(rect, (styleValue & 2) == 2, k_ItalicLabel, TMP_UIStyleManager.alignmentButtonMid) ? 2 : 0; // Italics
                rect.x += rect.width;
                v3 = TMP_EditorUtility.EditorToggle(rect, (styleValue & 4) == 4, k_UnderlineLabel, TMP_UIStyleManager.alignmentButtonMid) ? 4 : 0; // Underline
                rect.x += rect.width;
                v7 = TMP_EditorUtility.EditorToggle(rect, (styleValue & 64) == 64, k_StrikethroughLabel, TMP_UIStyleManager.alignmentButtonRight) ? 64 : 0; // Strikethrough
                rect.x += rect.width;

                int selected = 0;

                EditorGUI.BeginChangeCheck();
                v4 = TMP_EditorUtility.EditorToggle(rect, (styleValue & 8) == 8, k_LowercaseLabel, TMP_UIStyleManager.alignmentButtonLeft) ? 8 : 0; // Lowercase
                if (EditorGUI.EndChangeCheck() && v4 > 0)
                {
                    selected = v4;
                }
                rect.x += rect.width;
                EditorGUI.BeginChangeCheck();
                v5 = TMP_EditorUtility.EditorToggle(rect, (styleValue & 16) == 16, k_UppercaseLabel, TMP_UIStyleManager.alignmentButtonMid) ? 16 : 0; // Uppercase
                if (EditorGUI.EndChangeCheck() && v5 > 0)
                {
                    selected = v5;
                }
                rect.x += rect.width;
                EditorGUI.BeginChangeCheck();
                v6 = TMP_EditorUtility.EditorToggle(rect, (styleValue & 32) == 32, k_SmallcapsLabel, TMP_UIStyleManager.alignmentButtonRight) ? 32 : 0; // Smallcaps
                if (EditorGUI.EndChangeCheck() && v6 > 0)
                {
                    selected = v6;
                }

                if (selected > 0)
                {
                    v4 = selected == 8 ? 8 : 0;
                    v5 = selected == 16 ? 16 : 0;
                    v6 = selected == 32 ? 32 : 0;
                }

                EditorGUI.EndProperty();
            }
            else
            {
                rect = EditorGUILayout.GetControlRect(true, EditorGUIUtility.singleLineHeight + 2f);

                EditorGUI.BeginProperty(rect, k_FontStyleLabel, m_FontStyleProp);

                EditorGUI.PrefixLabel(rect, k_FontStyleLabel);

                int styleValue = m_FontStyleProp.intValue;

                rect.x += EditorGUIUtility.labelWidth;
                rect.width -= EditorGUIUtility.labelWidth;
                rect.width = Mathf.Max(25f, rect.width / 4f);

                v1 = TMP_EditorUtility.EditorToggle(rect, (styleValue & 1) == 1, k_BoldLabel, TMP_UIStyleManager.alignmentButtonLeft) ? 1 : 0; // Bold
                rect.x += rect.width;
                v2 = TMP_EditorUtility.EditorToggle(rect, (styleValue & 2) == 2, k_ItalicLabel, TMP_UIStyleManager.alignmentButtonMid) ? 2 : 0; // Italics
                rect.x += rect.width;
                v3 = TMP_EditorUtility.EditorToggle(rect, (styleValue & 4) == 4, k_UnderlineLabel, TMP_UIStyleManager.alignmentButtonMid) ? 4 : 0; // Underline
                rect.x += rect.width;
                v7 = TMP_EditorUtility.EditorToggle(rect, (styleValue & 64) == 64, k_StrikethroughLabel, TMP_UIStyleManager.alignmentButtonRight) ? 64 : 0; // Strikethrough

                rect = EditorGUILayout.GetControlRect(true, EditorGUIUtility.singleLineHeight + 2f);

                rect.x += EditorGUIUtility.labelWidth;
                rect.width -= EditorGUIUtility.labelWidth;

                rect.width = Mathf.Max(25f, rect.width / 4f);

                int selected = 0;

                EditorGUI.BeginChangeCheck();
                v4 = TMP_EditorUtility.EditorToggle(rect, (styleValue & 8) == 8, k_LowercaseLabel, TMP_UIStyleManager.alignmentButtonLeft) ? 8 : 0; // Lowercase
                if (EditorGUI.EndChangeCheck() && v4 > 0)
                {
                    selected = v4;
                }
                rect.x += rect.width;
                EditorGUI.BeginChangeCheck();
                v5 = TMP_EditorUtility.EditorToggle(rect, (styleValue & 16) == 16, k_UppercaseLabel, TMP_UIStyleManager.alignmentButtonMid) ? 16 : 0; // Uppercase
                if (EditorGUI.EndChangeCheck() && v5 > 0)
                {
                    selected = v5;
                }
                rect.x += rect.width;
                EditorGUI.BeginChangeCheck();
                v6 = TMP_EditorUtility.EditorToggle(rect, (styleValue & 32) == 32, k_SmallcapsLabel, TMP_UIStyleManager.alignmentButtonRight) ? 32 : 0; // Smallcaps
                if (EditorGUI.EndChangeCheck() && v6 > 0)
                {
                    selected = v6;
                }

                if (selected > 0)
                {
                    v4 = selected == 8 ? 8 : 0;
                    v5 = selected == 16 ? 16 : 0;
                    v6 = selected == 32 ? 32 : 0;
                }

                EditorGUI.EndProperty();
            }

            if (EditorGUI.EndChangeCheck())
            {
                m_FontStyleProp.intValue = v1 + v2 + v3 + v4 + v5 + v6 + v7;
                m_HavePropertiesChanged = true;
            }

            // FONT SIZE
            EditorGUI.BeginChangeCheck();

            EditorGUI.BeginDisabledGroup(m_AutoSizingProp.boolValue);
            EditorGUILayout.PropertyField(m_FontSizeProp, k_FontSizeLabel, GUILayout.MaxWidth(EditorGUIUtility.labelWidth + 50f));
            EditorGUI.EndDisabledGroup();

            if (EditorGUI.EndChangeCheck())
            {
                float fontSize = Mathf.Clamp(m_FontSizeProp.floatValue, 0, 32767);

                m_FontSizeProp.floatValue = fontSize;
                m_FontSizeBaseProp.floatValue = fontSize;
                m_HavePropertiesChanged = true;
            }

            EditorGUI.indentLevel += 1;

            EditorGUI.BeginChangeCheck();
            EditorGUILayout.PropertyField(m_AutoSizingProp, k_AutoSizeLabel);
            if (EditorGUI.EndChangeCheck())
            {
                if (m_AutoSizingProp.boolValue == false)
                    m_FontSizeProp.floatValue = m_FontSizeBaseProp.floatValue;

                m_HavePropertiesChanged = true;
            }

            // Show auto sizing options
            if (m_AutoSizingProp.boolValue)
            {
                rect = EditorGUILayout.GetControlRect(true, EditorGUIUtility.singleLineHeight);

                EditorGUI.PrefixLabel(rect, k_AutoSizeOptionsLabel);

                int previousIndent = EditorGUI.indentLevel;

                EditorGUI.indentLevel = 0;

                rect.width = (rect.width - EditorGUIUtility.labelWidth) / 4f;
                rect.x += EditorGUIUtility.labelWidth;

                EditorGUIUtility.labelWidth = 24;
                EditorGUI.BeginChangeCheck();
                EditorGUI.PropertyField(rect, m_FontSizeMinProp, k_MinLabel);
                if (EditorGUI.EndChangeCheck())
                {
                    float minSize = m_FontSizeMinProp.floatValue;

                    minSize = Mathf.Max(0, minSize);

                    m_FontSizeMinProp.floatValue = Mathf.Min(minSize, m_FontSizeMaxProp.floatValue);
                    m_HavePropertiesChanged = true;
                }
                rect.x += rect.width;

                EditorGUIUtility.labelWidth = 27;
                EditorGUI.BeginChangeCheck();
                EditorGUI.PropertyField(rect, m_FontSizeMaxProp, k_MaxLabel);
                if (EditorGUI.EndChangeCheck())
                {
                    float maxSize = Mathf.Clamp(m_FontSizeMaxProp.floatValue, 0, 32767);

                    m_FontSizeMaxProp.floatValue = Mathf.Max(m_FontSizeMinProp.floatValue, maxSize);
                    m_HavePropertiesChanged = true;
                }
                rect.x += rect.width;

                EditorGUI.BeginChangeCheck();
                EditorGUIUtility.labelWidth = 36;
                EditorGUI.PropertyField(rect, m_CharWidthMaxAdjProp, k_WdLabel);
                rect.x += rect.width;
                EditorGUIUtility.labelWidth = 28;
                EditorGUI.PropertyField(rect, m_LineSpacingMaxProp, k_LineLabel);

                EditorGUIUtility.labelWidth = 0;

                if (EditorGUI.EndChangeCheck())
                {
                    m_CharWidthMaxAdjProp.floatValue = Mathf.Clamp(m_CharWidthMaxAdjProp.floatValue, 0, 50);
                    m_LineSpacingMaxProp.floatValue = Mathf.Min(0, m_LineSpacingMaxProp.floatValue);
                    m_HavePropertiesChanged = true;
                }

                EditorGUI.indentLevel = previousIndent;
            }

            EditorGUI.indentLevel -= 1;



            EditorGUILayout.Space();
        }

        void DrawColor()
        {
            // FACE VERTEX COLOR
            EditorGUI.BeginChangeCheck();
            EditorGUILayout.PropertyField(m_FontColorProp, k_BaseColorLabel);
            if (EditorGUI.EndChangeCheck())
            {
                m_HavePropertiesChanged = true;
            }

            EditorGUI.BeginChangeCheck();
            EditorGUILayout.PropertyField(m_EnableVertexGradientProp, k_ColorGradientLabel);
            if (EditorGUI.EndChangeCheck())
            {
                m_HavePropertiesChanged = true;
            }

            EditorGUIUtility.fieldWidth = 0;

            if (m_EnableVertexGradientProp.boolValue)
            {
                EditorGUI.indentLevel += 1;

                EditorGUI.BeginChangeCheck();

                EditorGUILayout.PropertyField(m_FontColorGradientPresetProp, k_ColorPresetLabel);

                SerializedObject obj = null;

                SerializedProperty colorMode;

                SerializedProperty topLeft;
                SerializedProperty topRight;
                SerializedProperty bottomLeft;
                SerializedProperty bottomRight;

                if (m_FontColorGradientPresetProp.objectReferenceValue == null)
                {
                    colorMode = m_ColorModeProp;
                    topLeft = m_FontColorGradientProp.FindPropertyRelative("topLeft");
                    topRight = m_FontColorGradientProp.FindPropertyRelative("topRight");
                    bottomLeft = m_FontColorGradientProp.FindPropertyRelative("bottomLeft");
                    bottomRight = m_FontColorGradientProp.FindPropertyRelative("bottomRight");
                }
                else
                {
                    obj = new SerializedObject(m_FontColorGradientPresetProp.objectReferenceValue);
                    colorMode = obj.FindProperty("colorMode");
                    topLeft = obj.FindProperty("topLeft");
                    topRight = obj.FindProperty("topRight");
                    bottomLeft = obj.FindProperty("bottomLeft");
                    bottomRight = obj.FindProperty("bottomRight");
                }

                EditorGUILayout.PropertyField(colorMode, k_ColorModeLabel);

                Rect rect = EditorGUILayout.GetControlRect(true, EditorGUIUtility.singleLineHeight * (EditorGUIUtility.wideMode ? 1 : 2));

                EditorGUI.PrefixLabel(rect, k_CorenerColorsLabel);

                rect.x += EditorGUIUtility.labelWidth;
                rect.width = rect.width - EditorGUIUtility.labelWidth;

                switch ((ColorMode)colorMode.enumValueIndex)
                {
                    case ColorMode.Single:
                        TMP_EditorUtility.DrawColorProperty(rect, topLeft);

                        topRight.colorValue = topLeft.colorValue;
                        bottomLeft.colorValue = topLeft.colorValue;
                        bottomRight.colorValue = topLeft.colorValue;
                        break;
                    case ColorMode.HorizontalGradient:
                        rect.width /= 2f;

                        TMP_EditorUtility.DrawColorProperty(rect, topLeft);

                        rect.x += rect.width;

                        TMP_EditorUtility.DrawColorProperty(rect, topRight);

                        bottomLeft.colorValue = topLeft.colorValue;
                        bottomRight.colorValue = topRight.colorValue;
                        break;
                    case ColorMode.VerticalGradient:
                        TMP_EditorUtility.DrawColorProperty(rect, topLeft);

                        rect = EditorGUILayout.GetControlRect(false, EditorGUIUtility.singleLineHeight * (EditorGUIUtility.wideMode ? 1 : 2));
                        rect.x += EditorGUIUtility.labelWidth;

                        TMP_EditorUtility.DrawColorProperty(rect, bottomLeft);

                        topRight.colorValue = topLeft.colorValue;
                        bottomRight.colorValue = bottomLeft.colorValue;
                        break;
                    case ColorMode.FourCornersGradient:
                        rect.width /= 2f;

                        TMP_EditorUtility.DrawColorProperty(rect, topLeft);

                        rect.x += rect.width;

                        TMP_EditorUtility.DrawColorProperty(rect, topRight);

                        rect = EditorGUILayout.GetControlRect(false, EditorGUIUtility.singleLineHeight * (EditorGUIUtility.wideMode ? 1 : 2));
                        rect.x += EditorGUIUtility.labelWidth;
                        rect.width = (rect.width - EditorGUIUtility.labelWidth) / 2f;

                        TMP_EditorUtility.DrawColorProperty(rect, bottomLeft);

                        rect.x += rect.width;

                        TMP_EditorUtility.DrawColorProperty(rect, bottomRight);
                        break;
                }

                if (EditorGUI.EndChangeCheck())
                {
                    m_HavePropertiesChanged = true;
                    if (obj != null)
                    {
                        obj.ApplyModifiedProperties();
                        TMPro_EventManager.ON_COLOR_GRADIENT_PROPERTY_CHANGED(m_FontColorGradientPresetProp.objectReferenceValue as TMP_ColorGradient);
                    }
                }

                EditorGUI.indentLevel -= 1;
            }

            EditorGUILayout.PropertyField(m_OverrideHtmlColorProp, k_OverrideTagsLabel);

            EditorGUILayout.Space();
        }

        void DrawSpacing()
        {
            // CHARACTER, LINE & PARAGRAPH SPACING
            EditorGUI.BeginChangeCheck();

            Rect rect = EditorGUILayout.GetControlRect(true, EditorGUIUtility.singleLineHeight);

            EditorGUI.PrefixLabel(rect, k_SpacingOptionsLabel);

            int oldIndent = EditorGUI.indentLevel;
            EditorGUI.indentLevel = 0;

            float currentLabelWidth = EditorGUIUtility.labelWidth;
            rect.x += currentLabelWidth;
            rect.width = (rect.width - currentLabelWidth - 3f) / 2f;

            EditorGUIUtility.labelWidth = Mathf.Min(rect.width * 0.55f, 80f);

            EditorGUI.PropertyField(rect, m_CharacterSpacingProp, k_CharacterSpacingLabel);
            rect.x += rect.width + 3f;
            EditorGUI.PropertyField(rect, m_WordSpacingProp, k_WordSpacingLabel);

            rect = EditorGUILayout.GetControlRect(false, EditorGUIUtility.singleLineHeight);

            rect.x += currentLabelWidth;
            rect.width = (rect.width - currentLabelWidth -3f) / 2f;
            EditorGUIUtility.labelWidth = Mathf.Min(rect.width * 0.55f, 80f);

            EditorGUI.PropertyField(rect, m_LineSpacingProp, k_LineSpacingLabel);
            rect.x += rect.width + 3f;
            EditorGUI.PropertyField(rect, m_ParagraphSpacingProp, k_ParagraphSpacingLabel);

            EditorGUIUtility.labelWidth = currentLabelWidth;
            EditorGUI.indentLevel = oldIndent;

            if (EditorGUI.EndChangeCheck())
            {
                m_HavePropertiesChanged = true;
            }

            EditorGUILayout.Space();
        }

        void DrawAlignment()
        {
            // TEXT ALIGNMENT
            EditorGUI.BeginChangeCheck();

            Rect rect = EditorGUILayout.GetControlRect(true, EditorGUIUtility.currentViewWidth > 504 ? 20 : 40 + 3);
            EditorGUI.BeginProperty(rect, k_AlignmentLabel, m_HorizontalAlignmentProp);
            EditorGUI.BeginProperty(rect, k_AlignmentLabel, m_VerticalAlignmentProp);

            EditorGUI.PrefixLabel(rect, k_AlignmentLabel);
            rect.x += EditorGUIUtility.labelWidth;

            EditorGUI.PropertyField(rect, m_HorizontalAlignmentProp, GUIContent.none);
            EditorGUI.PropertyField(rect, m_VerticalAlignmentProp, GUIContent.none);

            // WRAPPING RATIOS shown if Justified mode is selected.
            if (((HorizontalAlignmentOptions)m_HorizontalAlignmentProp.intValue & HorizontalAlignmentOptions.Justified) == HorizontalAlignmentOptions.Justified || ((HorizontalAlignmentOptions)m_HorizontalAlignmentProp.intValue & HorizontalAlignmentOptions.Flush) == HorizontalAlignmentOptions.Flush)
                DrawPropertySlider(k_WrapMixLabel, m_WordWrappingRatiosProp);

            if (EditorGUI.EndChangeCheck())
                m_HavePropertiesChanged = true;

            EditorGUI.EndProperty();
            EditorGUI.EndProperty();

            EditorGUILayout.Space();
        }

        void DrawWrappingOverflow()
        {
            // TEXT WRAPPING
            Rect rect = EditorGUILayout.GetControlRect(true, EditorGUIUtility.singleLineHeight);
            EditorGUI.BeginProperty(rect, k_WrappingLabel, m_EnableWordWrappingProp);

            EditorGUI.BeginChangeCheck();
            int wrapSelection = EditorGUI.Popup(rect, k_WrappingLabel, m_EnableWordWrappingProp.boolValue ? 1 : 0, k_WrappingOptions);
            if (EditorGUI.EndChangeCheck())
            {
                m_EnableWordWrappingProp.boolValue = wrapSelection == 1;
                m_HavePropertiesChanged = true;
            }

            EditorGUI.EndProperty();

            // TEXT OVERFLOW
            EditorGUI.BeginChangeCheck();

            if ((TextOverflowModes)m_TextOverflowModeProp.enumValueIndex == TextOverflowModes.Linked)
            {
                EditorGUILayout.BeginHorizontal();

                float fieldWidth = EditorGUIUtility.fieldWidth;
                EditorGUIUtility.fieldWidth = 65;
                EditorGUILayout.PropertyField(m_TextOverflowModeProp, k_OverflowLabel);
                EditorGUIUtility.fieldWidth = fieldWidth;

                EditorGUILayout.PropertyField(m_LinkedTextComponentProp, GUIContent.none);

                EditorGUILayout.EndHorizontal();

                if (GUI.changed)
                {
                    TMP_Text linkedComponent = m_LinkedTextComponentProp.objectReferenceValue as TMP_Text;

                    if (linkedComponent == null)
                    {
                        m_LinkedTextComponentProp.objectReferenceValue = null;

                        if (m_PreviousLinkedTextComponent != null)
                            m_TextComponent.ReleaseLinkedTextComponent(m_PreviousLinkedTextComponent);
                    }
                    else if (m_TextComponent.IsSelfOrLinkedAncestor(linkedComponent))
                    {
                        m_LinkedTextComponentProp.objectReferenceValue = m_PreviousLinkedTextComponent;
                    }
                    else
                    {
                        if (m_PreviousLinkedTextComponent != null)
                            m_TextComponent.ReleaseLinkedTextComponent(m_PreviousLinkedTextComponent);

                        m_LinkedTextComponentProp.objectReferenceValue = linkedComponent;
                        linkedComponent.parentLinkedComponent = m_TextComponent;
                        m_PreviousLinkedTextComponent = linkedComponent;
                    }
                }
            }
            else if ((TextOverflowModes)m_TextOverflowModeProp.enumValueIndex == TextOverflowModes.Page)
            {
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.PropertyField(m_TextOverflowModeProp, k_OverflowLabel);
                EditorGUILayout.PropertyField(m_PageToDisplayProp, GUIContent.none);
                EditorGUILayout.EndHorizontal();

                if (m_PreviousLinkedTextComponent)
                {
                    m_TextComponent.ReleaseLinkedTextComponent(m_PreviousLinkedTextComponent);

                    m_TextComponent.linkedTextComponent = null;
                }
            }
            else
            {
                EditorGUILayout.PropertyField(m_TextOverflowModeProp, k_OverflowLabel);

                if (m_PreviousLinkedTextComponent)
                {
                    m_TextComponent.ReleaseLinkedTextComponent(m_PreviousLinkedTextComponent);

                    m_TextComponent.linkedTextComponent = null;
                }
            }

            if (EditorGUI.EndChangeCheck())
            {
                m_HavePropertiesChanged = true;
            }

            EditorGUILayout.Space();
        }

        protected abstract void DrawExtraSettings();

        protected void DrawMargins()
        {
            EditorGUI.BeginChangeCheck();
            DrawMarginProperty(m_MarginProp, k_MarginsLabel);
            if (EditorGUI.EndChangeCheck())
            {
                m_HavePropertiesChanged = true;
            }

            EditorGUILayout.Space();
        }

        protected void DrawGeometrySorting()
        {
            EditorGUI.BeginChangeCheck();

            EditorGUILayout.PropertyField(m_GeometrySortingOrderProp, k_GeometrySortingLabel);

            if (EditorGUI.EndChangeCheck())
                m_HavePropertiesChanged = true;

            EditorGUILayout.Space();
        }

        protected void DrawIsTextObjectScaleStatic()
        {
            EditorGUI.BeginChangeCheck();

            EditorGUILayout.PropertyField(m_IsTextObjectScaleStaticProp, k_IsTextObjectScaleStatic);

            if (EditorGUI.EndChangeCheck())
            {
                m_TextComponent.isTextObjectScaleStatic = m_IsTextObjectScaleStaticProp.boolValue;
                m_HavePropertiesChanged = true;
            }

            EditorGUILayout.Space();
        }


        protected void DrawRichText()
        {
            EditorGUI.BeginChangeCheck();

            EditorGUILayout.PropertyField(m_IsRichTextProp, k_RichTextLabel);
            if (EditorGUI.EndChangeCheck())
                m_HavePropertiesChanged = true;
        }

        protected void DrawParsing()
        {
            EditorGUI.BeginChangeCheck();
            EditorGUILayout.PropertyField(m_EnableEscapeCharacterParsingProp, k_EscapeCharactersLabel);
            EditorGUILayout.PropertyField(m_UseMaxVisibleDescenderProp, k_VisibleDescenderLabel);

            if (EditorGUI.EndChangeCheck())
                m_HavePropertiesChanged = true;

            EditorGUILayout.Space();
        }

        protected void DrawSpriteAsset()
        {
            EditorGUI.BeginChangeCheck();

            EditorGUILayout.PropertyField(m_SpriteAssetProp, k_SpriteAssetLabel, true);

            if (EditorGUI.EndChangeCheck())
                m_HavePropertiesChanged = true;

            EditorGUILayout.Space();
        }

        protected void DrawStyleSheet()
        {
            EditorGUI.BeginChangeCheck();

            EditorGUILayout.PropertyField(m_StyleSheetAssetProp, k_StyleSheetAssetLabel, true);

            if (EditorGUI.EndChangeCheck())
            {
                m_StyleNames = GetStyleNames();
                m_HavePropertiesChanged = true;
            }

            EditorGUILayout.Space();
        }

        protected void DrawTextureMapping()
        {
            // TEXTURE MAPPING OPTIONS
            EditorGUI.BeginChangeCheck();
            EditorGUILayout.PropertyField(m_HorizontalMappingProp, k_HorizontalMappingLabel);
            EditorGUILayout.PropertyField(m_VerticalMappingProp, k_VerticalMappingLabel);
            if (EditorGUI.EndChangeCheck())
            {
                m_HavePropertiesChanged = true;
            }

            // UV OPTIONS
            if (m_HorizontalMappingProp.enumValueIndex > 0)
            {
                EditorGUI.BeginChangeCheck();
                EditorGUILayout.PropertyField(m_UvLineOffsetProp, k_LineOffsetLabel, GUILayout.MinWidth(70f));
                if (EditorGUI.EndChangeCheck())
                {
                    m_HavePropertiesChanged = true;
                }
            }

            EditorGUILayout.Space();
        }

        protected void DrawKerning()
        {
            // KERNING
            EditorGUI.BeginChangeCheck();
            EditorGUILayout.PropertyField(m_EnableKerningProp, k_KerningLabel);
            if (EditorGUI.EndChangeCheck())
            {
                m_HavePropertiesChanged = true;
            }
        }

        protected void DrawPadding()
        {
            // EXTRA PADDING
            EditorGUI.BeginChangeCheck();
            EditorGUILayout.PropertyField(m_EnableExtraPaddingProp, k_PaddingLabel);
            if (EditorGUI.EndChangeCheck())
            {
                m_HavePropertiesChanged = true;
                m_CheckPaddingRequiredProp.boolValue = true;
            }
        }

        /// <summary>
        /// Method to retrieve the material presets that match the currently selected font asset.
        /// </summary>
        protected GUIContent[] GetMaterialPresets()
        {
            TMP_FontAsset fontAsset = m_FontAssetProp.objectReferenceValue as TMP_FontAsset;
            if (fontAsset == null) return null;

            m_MaterialPresets = TMP_EditorUtility.FindMaterialReferences(fontAsset);
            m_MaterialPresetNames = new GUIContent[m_MaterialPresets.Length];

            m_MaterialPresetIndexLookup.Clear();

            for (int i = 0; i < m_MaterialPresetNames.Length; i++)
            {
                m_MaterialPresetNames[i] = new GUIContent(m_MaterialPresets[i].name);

                m_MaterialPresetIndexLookup.Add(m_MaterialPresets[i].GetInstanceID(), i);

                //if (m_TargetMaterial.GetInstanceID() == m_MaterialPresets[i].GetInstanceID())
                //    m_MaterialPresetSelectionIndex = i;
            }

            m_IsPresetListDirty = false;

            // 按分类组织材质
            OrganizeMaterialPresetsByCategory();

            return m_MaterialPresetNames;
        }

        /// <summary>
        /// 解析材质名称获取分类信息
        /// </summary>
        protected MaterialCategoryInfo ParseMaterialName(string materialName)
        {
            var info = new MaterialCategoryInfo();

            // 匹配模式: TMP_Font_SDF_O1_000000 或 TMP_Font_SDF_S2_FF0000
            var match = Regex.Match(materialName, @"TMP_Font_SDF_([OS])(\d+)_([0-9A-Fa-f]{6})");

            if (match.Success)
            {
                string type = match.Groups[1].Value; // O 或 S
                string pixel = match.Groups[2].Value; // 数字
                string colorHex = match.Groups[3].Value; // 颜色十六进制

                info.category = type + pixel; // O1, O2, S1, S2 等
                info.colorHex = colorHex;
                info.isDefault = false;
            }
            else
            {
                // 默认材质或其他格式
                info.category = "默认";
                info.colorHex = "FFFFFF";
                info.isDefault = true;
            }

            info.displayName = materialName;
            return info;
        }

        /// <summary>
        /// 按分类组织材质预设
        /// </summary>
        protected void OrganizeMaterialPresetsByCategory()
        {
            m_MaterialPresetsByCategory.Clear();

            if (m_MaterialPresets == null) return;

            foreach (var material in m_MaterialPresets)
            {
                var categoryInfo = ParseMaterialName(material.name);

                if (!m_MaterialPresetsByCategory.ContainsKey(categoryInfo.category))
                {
                    m_MaterialPresetsByCategory[categoryInfo.category] = new List<Material>();
                }

                m_MaterialPresetsByCategory[categoryInfo.category].Add(material);
            }

            // 排序分类，默认在前，然后按字母顺序
            var categories = m_MaterialPresetsByCategory.Keys.ToList();
            categories.Sort((a, b) => {
                if (a == "默认") return -1;
                if (b == "默认") return 1;
                return a.CompareTo(b);
            });

            m_MaterialCategories = categories.ToArray();

            // 智能分类切换逻辑
            Material currentMaterial = m_FontSharedMaterialProp.objectReferenceValue as Material;

            // 只在以下情况自动切换到当前材质所在的分类：
            // 1. 正在初始化（第一次加载）
            // 2. 材质发生了变化，但不是用户在当前分类中手动选择的结果
            bool shouldAutoSwitch = false;

            if (m_IsInitializing)
            {
                // 初始化时总是自动切换
                shouldAutoSwitch = true;
                m_IsInitializing = false;
            }
            else if (currentMaterial != m_LastSelectedMaterial)
            {
                // 材质发生了变化，检查是否是外部变化（比如Undo/Redo、代码设置等）
                // 如果当前材质不在当前查看的分类中，说明是外部变化，应该自动切换
                if (currentMaterial != null && m_SelectedCategoryIndex >= 0 && m_SelectedCategoryIndex < m_MaterialCategories.Length)
                {
                    string selectedCategory = m_MaterialCategories[m_SelectedCategoryIndex];
                    if (m_MaterialPresetsByCategory.ContainsKey(selectedCategory))
                    {
                        var materialsInCategory = m_MaterialPresetsByCategory[selectedCategory];
                        if (!materialsInCategory.Contains(currentMaterial))
                        {
                            // 当前材质不在当前查看的分类中，说明是外部变化
                            shouldAutoSwitch = true;
                        }
                    }
                }
            }

            if (shouldAutoSwitch && currentMaterial != null)
            {
                var currentCategoryInfo = ParseMaterialName(currentMaterial.name);
                for (int i = 0; i < m_MaterialCategories.Length; i++)
                {
                    if (m_MaterialCategories[i] == currentCategoryInfo.category)
                    {
                        m_SelectedCategoryIndex = i;
                        break;
                    }
                }
            }

            // 更新记录的材质
            m_LastSelectedMaterial = currentMaterial;

            // 确保选择索引有效
            if (m_SelectedCategoryIndex < 0 || m_SelectedCategoryIndex >= m_MaterialCategories.Length)
            {
                m_SelectedCategoryIndex = 0;
            }
        }

        /// <summary>
        /// 材质分类信息结构
        /// </summary>
        protected struct MaterialCategoryInfo
        {
            public string category;
            public string colorHex;
            public string displayName;
            public bool isDefault;
        }

        /// <summary>
        /// 绘制自定义的MaterialPreset GUI
        /// </summary>
        protected void DrawCustomMaterialPresetGUI()
        {
            if (m_MaterialCategories == null || m_MaterialCategories.Length == 0)
            {
                GUILayout.Label("没有找到材质预设", EditorStyles.helpBox);
                return;
            }

            // 确保选择索引有效
            if (m_SelectedCategoryIndex < 0 || m_SelectedCategoryIndex >= m_MaterialCategories.Length)
                m_SelectedCategoryIndex = 0;

            EditorGUI.BeginChangeCheck();

            // 绘制分类按钮
            GUILayout.Label("Material Preset", EditorStyles.boldLabel);

            // 获取当前选中材质的分类
            Material currentMaterial = m_FontSharedMaterialProp.objectReferenceValue as Material;
            // 当前材质所在分类
            string currentMaterialCategory = "";
            if (currentMaterial != null)
            {
                var currentCategoryInfo = ParseMaterialName(currentMaterial.name);
                currentMaterialCategory = currentCategoryInfo.category;
            }


            // 使用网格布局显示分类按钮
            int buttonsPerRow = Mathf.Min(8, m_MaterialCategories.Length);
            for (int row = 0; row < Mathf.CeilToInt((float)m_MaterialCategories.Length / buttonsPerRow); row++)
            {
                EditorGUILayout.BeginHorizontal();
                for (int col = 0; col < buttonsPerRow; col++)
                {
                    int index = row * buttonsPerRow + col;
                    if (index >= m_MaterialCategories.Length) break;

                    // 当前选中的分类按钮
                    bool isSelected = m_SelectedCategoryIndex == index;
                    bool isCurrentMaterialCategory = m_MaterialCategories[index] == currentMaterialCategory;

                    // 根据状态选择按钮样式和颜色
                    Color originalBackgroundColor = GUI.backgroundColor;
                    Color originalContentColor = GUI.contentColor;

                    if (isSelected)
                    {
                        GUI.backgroundColor = new Color(0.3f, 0.6f, 1f, 1f); // 蓝色
                    }

                    if (isCurrentMaterialCategory)
                    {
                        GUI.contentColor = new Color(1f, 0.9f, 0.2f, 1f); // 黄色
                    }

                    if (GUILayout.Button(m_MaterialCategories[index], EditorStyles.miniButton) && !isSelected)
                    {
                        m_SelectedCategoryIndex = index;
                    }

                    // 恢复原始颜色
                    GUI.backgroundColor = originalBackgroundColor;
                    GUI.contentColor = originalContentColor;
                }
                EditorGUILayout.EndHorizontal();
            }

            // 绘制当前分类的材质列表
            if (m_SelectedCategoryIndex >= 0 && m_SelectedCategoryIndex < m_MaterialCategories.Length)
            {
                string selectedCategory = m_MaterialCategories[m_SelectedCategoryIndex];
                var materialsInCategory = m_MaterialPresetsByCategory[selectedCategory];

                GUILayout.Space(5);
                GUILayout.Label($"分类: {selectedCategory} ({materialsInCategory.Count} 个材质)    实际颜色以材质球设置的颜色为主，此列表展示的只是材质球的名字", EditorStyles.miniLabel);

                EditorGUILayout.BeginVertical(EditorStyles.helpBox);
                m_MaterialScrollPosition = EditorGUILayout.BeginScrollView(m_MaterialScrollPosition, GUILayout.MaxHeight(200));

                foreach (var material in materialsInCategory)
                {
                    var categoryInfo = ParseMaterialName(material.name);

                    // 使用垂直居中、左对齐的水平布局
                    EditorGUILayout.BeginHorizontal(GUILayout.Height(EditorGUIUtility.singleLineHeight));

                    // 材质选择单选按钮
                    bool isCurrentMaterial = currentMaterial == material;
                    var buttonRect = GUILayoutUtility.GetRect(25, EditorGUIUtility.singleLineHeight, GUILayout.Width(25));
                    Color originalBackgroundColor = GUI.backgroundColor;
                    if (isCurrentMaterial)
                    {
                        GUI.backgroundColor = new Color(1f, 0.9f, 0.2f, 1f); // 黄色
                    }

                    if (GUI.Button(buttonRect, isCurrentMaterial ? "●" : "○", EditorStyles.miniButton) && !isCurrentMaterial)
                    {
                        m_FontSharedMaterialProp.objectReferenceValue = material;
                        m_HavePropertiesChanged = true;
                    }
                    GUI.backgroundColor = originalBackgroundColor;

                    GUILayout.Space(10);
                    // 颜色十六进制显示
                    var hexRect = GUILayoutUtility.GetRect(60, EditorGUIUtility.singleLineHeight, GUILayout.Width(50));
                    var hexStyle = new GUIStyle(EditorStyles.miniLabel) { alignment = TextAnchor.MiddleLeft };
                    GUI.Label(hexRect, categoryInfo.colorHex.ToUpper(), hexStyle);

                    // 颜色预览
                    if (ColorUtility.TryParseHtmlString("#" + categoryInfo.colorHex, out Color color))
                    {
                        var colorRect = GUILayoutUtility.GetRect(40, EditorGUIUtility.singleLineHeight - 2, GUILayout.Width(40));
                        colorRect.y += 1;
                        var oldColor = GUI.color;
                        GUI.color = color;
                        GUI.Box(colorRect, "");
                        GUI.color = oldColor;
                    }

                    GUILayout.Space(10);

                    // 材质球引用
                    var materialRect = GUILayoutUtility.GetRect(200, EditorGUIUtility.singleLineHeight, GUILayout.Width(200));
                    // 禁用编辑但保持可点击
                    EditorGUI.BeginDisabledGroup(true);
                    EditorGUI.ObjectField(materialRect, material, typeof(Material), false);
                    EditorGUI.EndDisabledGroup();
                    // 检测材质球区域的点击事件来实现快速定位
                    if (Event.current.type == EventType.MouseDown && materialRect.Contains(Event.current.mousePosition))
                    {
                        EditorGUIUtility.PingObject(material);
                        Selection.activeObject = material;
                        Event.current.Use();
                    }

                    EditorGUILayout.EndHorizontal();

                    // 分隔线
                    if (material != materialsInCategory.Last())
                    {
                        GUILayout.Space(2);
                        var rect = GUILayoutUtility.GetRect(1, 1, GUILayout.ExpandWidth(true));
                        EditorGUI.DrawRect(rect, new Color(0.5f, 0.5f, 0.5f, 0.3f));
                    }
                    GUILayout.Space(2);
                }

                EditorGUILayout.EndScrollView();
                EditorGUILayout.EndVertical();
            }

            if (EditorGUI.EndChangeCheck())
            {
                // 材质已在上面的循环中设置
            }
        }

        protected GUIContent[] GetStyleNames()
        {
            k_AvailableStyles.Clear();
            m_TextStyleIndexLookup.Clear();
            m_Styles.Clear();

            // First style on the list is always the Normal default style.
            TMP_Style styleNormal = TMP_Style.NormalStyle;

            m_Styles.Add(styleNormal);
            m_TextStyleIndexLookup.Add(styleNormal.hashCode, 0);

            k_AvailableStyles.Add(styleNormal.hashCode, styleNormal);

            // Get styles from Style Sheet potentially assigned to the text object.
            TMP_StyleSheet localStyleSheet = (TMP_StyleSheet)m_StyleSheetAssetProp.objectReferenceValue;

            if (localStyleSheet != null)
            {
                int styleCount = localStyleSheet.styles.Count;

                for (int i = 0; i < styleCount; i++)
                {
                    TMP_Style style = localStyleSheet.styles[i];

                    if (k_AvailableStyles.ContainsKey(style.hashCode) == false)
                    {
                        k_AvailableStyles.Add(style.hashCode, style);
                        m_Styles.Add(style);
                        m_TextStyleIndexLookup.Add(style.hashCode, m_TextStyleIndexLookup.Count);
                    }
                }
            }

            // Get styles from TMP Settings' default style sheet.
            TMP_StyleSheet globalStyleSheet = TMP_Settings.defaultStyleSheet;

            if (globalStyleSheet != null)
            {
                int styleCount = globalStyleSheet.styles.Count;

                for (int i = 0; i < styleCount; i++)
                {
                    TMP_Style style = globalStyleSheet.styles[i];

                    if (k_AvailableStyles.ContainsKey(style.hashCode) == false)
                    {
                        k_AvailableStyles.Add(style.hashCode, style);
                        m_Styles.Add(style);
                        m_TextStyleIndexLookup.Add(style.hashCode, m_TextStyleIndexLookup.Count);
                    }
                }
            }

            // Create array that will contain the list of available styles.
            GUIContent[] styleNames = k_AvailableStyles.Values.Select(item => new GUIContent(item.name)).ToArray();

            // Set text style index
            m_TextStyleIndexLookup.TryGetValue(m_TextStyleHashCodeProp.intValue, out m_StyleSelectionIndex);

            return styleNames;
        }

        // DRAW MARGIN PROPERTY
        protected void DrawMarginProperty(SerializedProperty property, GUIContent label)
        {
            Rect rect = EditorGUILayout.GetControlRect(false, 2 * 18);

            EditorGUI.BeginProperty(rect, label, property);

            Rect pos0 = new Rect(rect.x, rect.y + 2, rect.width - 15, 18);

            float width = rect.width + 3;
            pos0.width = EditorGUIUtility.labelWidth;
            EditorGUI.PrefixLabel(pos0, label);

            Vector4 margins = property.vector4Value;

            float widthB = width - EditorGUIUtility.labelWidth;
            float fieldWidth = widthB / 4;
            pos0.width = Mathf.Max(fieldWidth - 5, 45f);

            // Labels
            pos0.x = EditorGUIUtility.labelWidth + 15;
            margins.x = DrawMarginField(pos0, "Left", margins.x);

            pos0.x += fieldWidth;
            margins.y = DrawMarginField(pos0, "Top", margins.y);

            pos0.x += fieldWidth;
            margins.z = DrawMarginField(pos0, "Right", margins.z);

            pos0.x += fieldWidth;
            margins.w = DrawMarginField(pos0, "Bottom", margins.w);

            property.vector4Value = margins;

            EditorGUI.EndProperty();
        }

        float DrawMarginField(Rect position, string label, float value)
        {
            int controlId = GUIUtility.GetControlID(FocusType.Keyboard, position);
            EditorGUI.PrefixLabel(position, controlId, new GUIContent(label));

            Rect dragZone = new Rect(position.x, position.y, position.width, position.height);
            position.y += EditorGUIUtility.singleLineHeight;

            return EditorGUI.DoFloatField(EditorGUI.s_RecycledEditor, position, dragZone, controlId, value, EditorGUI.kFloatFieldFormatString, EditorStyles.numberField, true);
        }

        protected void DrawPropertySlider(GUIContent label, SerializedProperty property)
        {
            Rect rect = EditorGUILayout.GetControlRect(false, 17);

            GUIContent content = label ?? GUIContent.none;
            EditorGUI.Slider(new Rect(rect.x, rect.y, rect.width, rect.height), property, 0.0f, 1.0f, content);
        }

        protected abstract bool IsMixSelectionTypes();

        // Special Handling of Undo / Redo Events.
        protected abstract void OnUndoRedo();

    }
}
