using UnityEngine;
using UnityEditor;
using TMPro.EditorUtilities;

namespace TMPro
{
    /// <summary>
    /// 测试MaterialPreset分类功能的编辑器窗口
    /// </summary>
    public class TMP_MaterialPresetTest : EditorWindow
    {
        [MenuItem("TextMeshPro/Test Material Preset GUI")]
        static void ShowWindow()
        {
            GetWindow<TMP_MaterialPresetTest>("Material Preset Test");
        }

        void OnGUI()
        {
            GUILayout.Label("TextMeshPro Material Preset 分类测试", EditorStyles.boldLabel);
            GUILayout.Space(10);
            
            GUILayout.Label("使用说明:", EditorStyles.boldLabel);
            GUILayout.Label("1. 选择场景中的TextMeshPro组件");
            GUILayout.Label("2. 在Inspector中查看新的Material Preset界面");
            GUILayout.Label("3. 材质按分类显示：默认、O1、O2、O3、S1、S2、S3等");
            GUILayout.Label("4. 每个分类下显示对应的颜色列表");
            
            GUILayout.Space(10);
            
            if (GUILayout.Button("选择TextMeshPro对象"))
            {
                // 查找场景中的TextMeshPro对象
                var tmpObjects = FindObjectsOfType<TMP_Text>();
                if (tmpObjects.Length > 0)
                {
                    Selection.activeObject = tmpObjects[0];
                    EditorGUIUtility.PingObject(tmpObjects[0]);
                }
                else
                {
                    EditorUtility.DisplayDialog("提示", "场景中没有找到TextMeshPro对象", "确定");
                }
            }
            
            GUILayout.Space(10);
            
            GUILayout.Label("材质命名规律:", EditorStyles.boldLabel);
            GUILayout.Label("• TMP_Font_SDF_O1_000000 - Outline 1像素，黑色");
            GUILayout.Label("• TMP_Font_SDF_S2_FF0000 - Shadow 2像素，红色");
            GUILayout.Label("• 其他格式归类为'默认'");
        }
    }
}
